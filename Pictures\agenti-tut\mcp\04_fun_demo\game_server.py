#!/usr/bin/env python3
"""
🎮 Text Adventure Game MCP Server

A fun MCP server that implements a text-based adventure game!
This demonstrates:
- Stateful tools (game state persistence)
- Complex interactions between tools
- Rich storytelling through MCP
- Interactive gameplay via LLM

The <PERSON><PERSON> becomes the game master, using these tools to create an adventure!
"""

import json
import random
from typing import Dict, List, Optional
from mcp.server.fastmcp import FastMCP

# Create MCP server instance
mcp = FastMCP("Adventure Game Server")

# Game state storage (in a real server, this might be a database)
game_state = {
    "player": {
        "name": "",
        "health": 100,
        "inventory": [],
        "location": "forest_entrance",
        "experience": 0,
        "level": 1
    },
    "locations": {
        "forest_entrance": {
            "name": "Forest Entrance",
            "description": "You stand at the edge of a mysterious forest. Ancient trees tower above you, their branches creating a canopy that blocks most sunlight. A narrow path winds deeper into the woods.",
            "items": ["wooden_stick", "healing_herb"],
            "exits": {"north": "deep_forest", "east": "old_cabin"},
            "visited": False
        },
        "deep_forest": {
            "name": "Deep Forest",
            "description": "The forest grows darker and more mysterious. Strange sounds echo from the shadows. You notice glowing mushrooms providing an eerie blue light.",
            "items": ["magic_mushroom"],
            "exits": {"south": "forest_entrance", "west": "crystal_cave"},
            "visited": False,
            "monster": "forest_goblin"
        },
        "old_cabin": {
            "name": "Abandoned Cabin",
            "description": "A weathered wooden cabin sits in a small clearing. The door hangs open, creaking in the wind. Vines have begun to reclaim the structure.",
            "items": ["rusty_key", "old_map"],
            "exits": {"west": "forest_entrance"},
            "visited": False
        },
        "crystal_cave": {
            "name": "Crystal Cave",
            "description": "A magnificent cave filled with glowing crystals. The walls shimmer with magical energy, and you can hear the sound of dripping water echoing in the distance.",
            "items": ["magic_crystal", "treasure_chest"],
            "exits": {"east": "deep_forest"},
            "visited": False,
            "monster": "crystal_guardian"
        }
    },
    "monsters": {
        "forest_goblin": {"name": "Forest Goblin", "health": 30, "attack": 15, "defeated": False},
        "crystal_guardian": {"name": "Crystal Guardian", "health": 50, "attack": 20, "defeated": False}
    }
}

@mcp.tool()
async def start_game(player_name: str) -> str:
    """
    Start a new adventure game.
    
    Args:
        player_name: The name of the player character
        
    Returns:
        Game introduction and starting scenario
    """
    global game_state
    
    if not player_name or not player_name.strip():
        return "❌ Error: Please provide a player name to start the game!"
    
    # Reset game state
    game_state["player"]["name"] = player_name.strip().title()
    game_state["player"]["health"] = 100
    game_state["player"]["inventory"] = []
    game_state["player"]["location"] = "forest_entrance"
    game_state["player"]["experience"] = 0
    game_state["player"]["level"] = 1
    
    # Reset location visited status
    for location in game_state["locations"].values():
        location["visited"] = False
    
    # Reset monsters
    for monster in game_state["monsters"].values():
        monster["defeated"] = False
        if monster["name"] == "Forest Goblin":
            monster["health"] = 30
        elif monster["name"] == "Crystal Guardian":
            monster["health"] = 50
    
    current_location = game_state["locations"][game_state["player"]["location"]]
    current_location["visited"] = True
    
    return f"""🎮 Welcome to the Mystical Forest Adventure!

🧙‍♂️ Greetings, {game_state["player"]["name"]}! You are about to embark on a magical adventure.

📍 Current Location: {current_location["name"]}
{current_location["description"]}

💚 Health: {game_state["player"]["health"]}/100
🎒 Inventory: Empty
⭐ Level: {game_state["player"]["level"]} (XP: {game_state["player"]["experience"]})

🗺️ Available exits: {', '.join(current_location["exits"].keys())}
💎 Items here: {', '.join(current_location["items"]) if current_location["items"] else "None"}

🎯 Your quest: Explore the mystical forest, collect magical items, and discover its secrets!

Use the game tools to:
• look_around - Examine your surroundings
• move - Travel to different locations  
• take_item - Pick up items
• check_inventory - See what you're carrying
• get_game_status - Check your current status
"""

@mcp.tool()
async def look_around() -> str:
    """
    Look around the current location for details, items, and exits.
    
    Returns:
        Detailed description of the current location
    """
    current_loc = game_state["player"]["location"]
    location = game_state["locations"][current_loc]
    
    description = f"""🔍 Looking around {location["name"]}...

{location["description"]}

💎 Items you can take: {', '.join(location["items"]) if location["items"] else "None"}
🗺️ Exits: {', '.join(f"{direction} to {game_state['locations'][dest]['name']}" for direction, dest in location["exits"].items())}
"""
    
    # Check for monsters
    if "monster" in location and not game_state["monsters"][location["monster"]]["defeated"]:
        monster = game_state["monsters"][location["monster"]]
        description += f"\n⚔️ DANGER: A {monster['name']} blocks your path! (Health: {monster['health']})"
    
    return description

@mcp.tool()
async def move(direction: str) -> str:
    """
    Move to a different location.
    
    Args:
        direction: Direction to move (north, south, east, west)
        
    Returns:
        Result of the movement attempt
    """
    current_loc = game_state["player"]["location"]
    location = game_state["locations"][current_loc]
    
    direction = direction.lower().strip()
    
    if direction not in location["exits"]:
        available = ', '.join(location["exits"].keys())
        return f"❌ You can't go {direction} from here. Available directions: {available}"
    
    # Check for monsters blocking the path
    if "monster" in location and not game_state["monsters"][location["monster"]]["defeated"]:
        monster = game_state["monsters"][location["monster"]]
        return f"⚔️ The {monster['name']} blocks your path! You must defeat it first using the 'fight_monster' tool."
    
    # Move to new location
    new_location_key = location["exits"][direction]
    new_location = game_state["locations"][new_location_key]
    game_state["player"]["location"] = new_location_key
    
    # Mark as visited and give XP for first visit
    if not new_location["visited"]:
        new_location["visited"] = True
        game_state["player"]["experience"] += 10
        xp_message = " (+10 XP for discovering a new location!)"
    else:
        xp_message = ""
    
    result = f"""🚶‍♂️ You travel {direction} to {new_location["name"]}.{xp_message}

📍 {new_location["name"]}
{new_location["description"]}

💎 Items here: {', '.join(new_location["items"]) if new_location["items"] else "None"}
🗺️ Exits: {', '.join(new_location["exits"].keys())}
"""
    
    # Check for monsters in new location
    if "monster" in new_location and not game_state["monsters"][new_location["monster"]]["defeated"]:
        monster = game_state["monsters"][new_location["monster"]]
        result += f"\n⚔️ DANGER: A {monster['name']} appears! (Health: {monster['health']})"
    
    return result

@mcp.tool()
async def take_item(item_name: str) -> str:
    """
    Take an item from the current location.
    
    Args:
        item_name: Name of the item to take
        
    Returns:
        Result of taking the item
    """
    current_loc = game_state["player"]["location"]
    location = game_state["locations"][current_loc]
    
    item_name = item_name.lower().strip()
    
    if item_name not in location["items"]:
        available = ', '.join(location["items"]) if location["items"] else "None"
        return f"❌ There's no '{item_name}' here. Available items: {available}"
    
    # Add to inventory and remove from location
    location["items"].remove(item_name)
    game_state["player"]["inventory"].append(item_name)
    game_state["player"]["experience"] += 5
    
    # Special item effects
    item_effects = {
        "healing_herb": "💚 The healing herb restores 20 health!",
        "magic_mushroom": "✨ The magic mushroom gives you mystical energy! (+15 XP)",
        "magic_crystal": "🔮 The magic crystal pulses with power! (+25 XP)",
        "treasure_chest": "💰 You found treasure! (+50 XP)"
    }
    
    effect_message = ""
    if item_name == "healing_herb":
        game_state["player"]["health"] = min(100, game_state["player"]["health"] + 20)
        effect_message = " " + item_effects[item_name]
    elif item_name in item_effects:
        bonus_xp = {"magic_mushroom": 15, "magic_crystal": 25, "treasure_chest": 50}.get(item_name, 0)
        game_state["player"]["experience"] += bonus_xp
        effect_message = " " + item_effects[item_name]
    
    return f"✅ You take the {item_name.replace('_', ' ')}.{effect_message} (+5 XP)"

@mcp.tool()
async def check_inventory() -> str:
    """
    Check what items you're currently carrying.
    
    Returns:
        List of items in inventory
    """
    inventory = game_state["player"]["inventory"]
    
    if not inventory:
        return "🎒 Your inventory is empty."
    
    items_display = []
    for item in inventory:
        display_name = item.replace('_', ' ').title()
        items_display.append(f"• {display_name}")
    
    return f"""🎒 Your Inventory:
{chr(10).join(items_display)}

Total items: {len(inventory)}
"""

@mcp.tool()
async def fight_monster() -> str:
    """
    Fight the monster in the current location.
    
    Returns:
        Result of the battle
    """
    current_loc = game_state["player"]["location"]
    location = game_state["locations"][current_loc]
    
    if "monster" not in location:
        return "❌ There's no monster here to fight!"
    
    monster_key = location["monster"]
    monster = game_state["monsters"][monster_key]
    
    if monster["defeated"]:
        return f"✅ The {monster['name']} has already been defeated."
    
    # Simple combat system
    player_attack = random.randint(15, 25)
    monster_attack = random.randint(monster["attack"] - 5, monster["attack"] + 5)
    
    # Player attacks first
    monster["health"] -= player_attack
    combat_log = f"⚔️ You attack the {monster['name']} for {player_attack} damage!"
    
    if monster["health"] <= 0:
        monster["defeated"] = True
        xp_gain = 30 if monster_key == "forest_goblin" else 50
        game_state["player"]["experience"] += xp_gain
        
        return f"""{combat_log}

🎉 Victory! You defeated the {monster['name']}! (+{xp_gain} XP)

The path is now clear, and you can move freely through this area.
"""
    
    # Monster counter-attacks
    game_state["player"]["health"] -= monster_attack
    combat_log += f"\n💥 The {monster['name']} attacks you for {monster_attack} damage!"
    
    if game_state["player"]["health"] <= 0:
        game_state["player"]["health"] = 0
        return f"""{combat_log}

💀 You have been defeated! Game Over.

Use 'start_game' to begin a new adventure.
"""
    
    return f"""{combat_log}

🏥 Your Health: {game_state["player"]["health"]}/100
⚔️ {monster['name']} Health: {monster["health"]}

The battle continues! Fight again or use healing items if you have them.
"""

@mcp.tool()
async def get_game_status() -> str:
    """
    Get current game status including player stats and location.
    
    Returns:
        Complete game status
    """
    player = game_state["player"]
    current_loc = game_state["locations"][player["location"]]
    
    # Calculate level
    level = 1 + (player["experience"] // 100)
    if level != player["level"]:
        player["level"] = level
        level_up_msg = f"\n🎉 LEVEL UP! You are now level {level}!"
    else:
        level_up_msg = ""
    
    return f"""📊 Game Status for {player["name"]}:

💚 Health: {player["health"]}/100
⭐ Level: {player["level"]} (XP: {player["experience"]})
📍 Location: {current_loc["name"]}
🎒 Inventory: {len(player["inventory"])} items

🗺️ Locations Discovered:
{chr(10).join(f"• {loc['name']}" for loc in game_state["locations"].values() if loc["visited"])}

⚔️ Monsters Defeated:
{chr(10).join(f"• {monster['name']}" for monster in game_state["monsters"].values() if monster["defeated"]) or "None yet"}
{level_up_msg}
"""

def main():
    """Run the Adventure Game MCP server"""
    print("🎮 Starting Adventure Game MCP Server...")
    print("Available tools: start_game, look_around, move, take_item, check_inventory, fight_monster, get_game_status")
    print("Server is ready for epic adventures!")
    print("Use Ctrl+C to stop the server.")
    
    # Run the server with stdio transport
    mcp.run(transport="stdio")

if __name__ == "__main__":
    main()
