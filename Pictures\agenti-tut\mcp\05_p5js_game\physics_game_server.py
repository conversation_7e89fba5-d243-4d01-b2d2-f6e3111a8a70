#!/usr/bin/env python3
"""
🎮 P5.js Physics Game MCP Server

An MCP server that controls a physics-based game (like Angry Birds) that an LLM can play!
This demonstrates:
- Real-time game control via MCP
- Physics simulation coordination
- Strategic gameplay through AI
- Web-based visual interface

The LLM can analyze the game state, plan shots, and execute strategies!
"""

import asyncio
import json
import math
import random
from typing import Dict, List, Tuple, Optional
from mcp.server.fastmcp import FastMCP
import threading
import time
from dataclasses import dataclass, asdict

# Create MCP server instance
mcp = FastMCP("Physics Game Server")

@dataclass
class Vector2:
    x: float
    y: float
    
    def __add__(self, other):
        return Vector2(self.x + other.x, self.y + other.y)
    
    def __mul__(self, scalar):
        return Vector2(self.x * scalar, self.y * scalar)

@dataclass
class Target:
    id: int
    position: Vector2
    radius: float
    points: int
    hit: bool = False
    type: str = "normal"  # normal, bonus, moving

@dataclass
class Obstacle:
    id: int
    position: Vector2
    width: float
    height: float
    destroyed: bool = False
    health: int = 1

@dataclass
class Bird:
    position: Vector2
    velocity: Vector2
    radius: float = 10
    active: bool = True

@dataclass
class GameState:
    birds_remaining: int
    score: int
    targets: List[Target]
    obstacles: List[Obstacle]
    current_bird: Optional[Bird]
    launcher_position: Vector2
    game_over: bool = False
    level: int = 1

# Global game state
game_state = None
game_history = []

# Physics constants
GRAVITY = 0.5
WORLD_WIDTH = 800
WORLD_HEIGHT = 600
LAUNCHER_X = 100
LAUNCHER_Y = 500

def initialize_game():
    """Initialize a new game with targets and obstacles"""
    global game_state
    
    # Create targets at various positions
    targets = [
        Target(1, Vector2(600, 450), 25, 100, type="normal"),
        Target(2, Vector2(700, 400), 20, 150, type="bonus"),
        Target(3, Vector2(650, 350), 30, 100, type="normal"),
        Target(4, Vector2(750, 480), 15, 200, type="bonus"),
        Target(5, Vector2(550, 500), 35, 80, type="normal"),
    ]
    
    # Create obstacles
    obstacles = [
        Obstacle(1, Vector2(500, 480), 40, 80, health=2),
        Obstacle(2, Vector2(580, 520), 30, 60, health=1),
        Obstacle(3, Vector2(720, 520), 50, 40, health=3),
    ]
    
    game_state = GameState(
        birds_remaining=5,
        score=0,
        targets=targets,
        obstacles=obstacles,
        current_bird=None,
        launcher_position=Vector2(LAUNCHER_X, LAUNCHER_Y)
    )

def simulate_physics(bird: Bird, time_step: float = 0.1, max_time: float = 10.0) -> List[Vector2]:
    """Simulate bird trajectory and return path points"""
    trajectory = []
    pos = Vector2(bird.position.x, bird.position.y)
    vel = Vector2(bird.velocity.x, bird.velocity.y)
    
    t = 0
    while t < max_time and pos.y < WORLD_HEIGHT and pos.x < WORLD_WIDTH:
        trajectory.append(Vector2(pos.x, pos.y))
        
        # Update physics
        vel.y += GRAVITY * time_step
        pos.x += vel.x * time_step
        pos.y += vel.y * time_step
        
        # Check collisions with targets
        for target in game_state.targets:
            if not target.hit:
                dist = math.sqrt((pos.x - target.position.x)**2 + (pos.y - target.position.y)**2)
                if dist < (bird.radius + target.radius):
                    return trajectory
        
        # Check collisions with obstacles
        for obstacle in game_state.obstacles:
            if not obstacle.destroyed:
                if (obstacle.position.x <= pos.x <= obstacle.position.x + obstacle.width and
                    obstacle.position.y <= pos.y <= obstacle.position.y + obstacle.height):
                    return trajectory
        
        # Check ground collision
        if pos.y >= WORLD_HEIGHT - bird.radius:
            return trajectory
        
        t += time_step
    
    return trajectory

@mcp.tool()
async def start_new_game() -> str:
    """
    Start a new physics game session.
    
    Returns:
        Game initialization status and initial state
    """
    global game_state, game_history
    
    initialize_game()
    game_history = []
    
    targets_info = []
    for target in game_state.targets:
        targets_info.append(f"Target {target.id}: {target.type} at ({target.position.x:.0f}, {target.position.y:.0f}), {target.points} points")
    
    obstacles_info = []
    for obstacle in game_state.obstacles:
        obstacles_info.append(f"Obstacle {obstacle.id}: {obstacle.width}x{obstacle.height} at ({obstacle.position.x:.0f}, {obstacle.position.y:.0f})")
    
    return f"""🎮 New Physics Game Started!

🎯 Game Setup:
• World Size: {WORLD_WIDTH}x{WORLD_HEIGHT} pixels
• Launcher Position: ({LAUNCHER_X}, {LAUNCHER_Y})
• Birds Available: {game_state.birds_remaining}
• Gravity: {GRAVITY} pixels/frame²

🎯 Targets ({len(game_state.targets)} total):
{chr(10).join(targets_info)}

🧱 Obstacles ({len(game_state.obstacles)} total):
{chr(10).join(obstacles_info)}

💡 Strategy Tips:
• Higher angles for distant targets
• Lower angles for close targets
• Consider obstacle placement
• Bonus targets give more points!

Ready to launch! Use 'launch_bird' to take your first shot! 🚀"""

@mcp.tool()
async def get_game_state() -> str:
    """
    Get the current game state and status.
    
    Returns:
        Detailed current game information
    """
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    # Count remaining targets
    active_targets = [t for t in game_state.targets if not t.hit]
    destroyed_obstacles = [o for o in game_state.obstacles if o.destroyed]
    
    # Calculate potential score
    max_possible_score = sum(t.points for t in active_targets)
    
    status = f"""📊 Current Game State:

🎯 Score: {game_state.score} points
🐦 Birds Remaining: {game_state.birds_remaining}
🎯 Targets Remaining: {len(active_targets)}/{len(game_state.targets)}
🧱 Obstacles Destroyed: {len(destroyed_obstacles)}/{len(game_state.obstacles)}

🎯 Active Targets:"""
    
    for target in active_targets:
        status += f"\n  • Target {target.id}: {target.type} ({target.points} pts) at ({target.position.x:.0f}, {target.position.y:.0f})"
    
    if game_state.obstacles:
        status += f"\n\n🧱 Obstacles:"
        for obstacle in game_state.obstacles:
            state = "DESTROYED" if obstacle.destroyed else f"Health: {obstacle.health}"
            status += f"\n  • Obstacle {obstacle.id}: {state} at ({obstacle.position.x:.0f}, {obstacle.position.y:.0f})"
    
    status += f"\n\n💰 Max Possible Score: {max_possible_score} points"
    
    if game_state.game_over:
        status += f"\n\n🎮 GAME OVER! Final Score: {game_state.score}"
    elif len(active_targets) == 0:
        status += f"\n\n🎉 LEVEL COMPLETE! All targets destroyed!"
    
    return status

@mcp.tool()
async def launch_bird(angle: float, power: float) -> str:
    """
    Launch a bird with specified angle and power.
    
    Args:
        angle: Launch angle in degrees (0-90, where 0 is horizontal)
        power: Launch power percentage (0-100)
        
    Returns:
        Result of the bird launch and what it hit
    """
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    if game_state.birds_remaining <= 0:
        return "❌ No birds remaining! Game over."
    
    if game_state.game_over:
        return "❌ Game is over! Use 'start_new_game' to play again."
    
    # Validate inputs
    if not (0 <= angle <= 90):
        return "❌ Angle must be between 0 and 90 degrees!"
    
    if not (0 <= power <= 100):
        return "❌ Power must be between 0 and 100 percent!"
    
    # Convert angle to radians and calculate velocity
    angle_rad = math.radians(angle)
    max_velocity = 15  # Maximum launch speed
    velocity_magnitude = (power / 100) * max_velocity
    
    velocity = Vector2(
        velocity_magnitude * math.cos(angle_rad),
        -velocity_magnitude * math.sin(angle_rad)  # Negative because Y increases downward
    )
    
    # Create bird
    bird = Bird(
        position=Vector2(game_state.launcher_position.x, game_state.launcher_position.y),
        velocity=velocity
    )
    
    # Simulate trajectory
    trajectory = simulate_physics(bird)
    
    # Check what was hit
    hits = []
    points_scored = 0
    
    # Check target hits
    for target in game_state.targets:
        if not target.hit:
            for point in trajectory:
                dist = math.sqrt((point.x - target.position.x)**2 + (point.y - target.position.y)**2)
                if dist < (bird.radius + target.radius):
                    target.hit = True
                    points_scored += target.points
                    hits.append(f"🎯 HIT Target {target.id} ({target.type}) for {target.points} points!")
                    break
    
    # Check obstacle hits
    for obstacle in game_state.obstacles:
        if not obstacle.destroyed:
            for point in trajectory:
                if (obstacle.position.x <= point.x <= obstacle.position.x + obstacle.width and
                    obstacle.position.y <= point.y <= obstacle.position.y + obstacle.height):
                    obstacle.health -= 1
                    if obstacle.health <= 0:
                        obstacle.destroyed = True
                        hits.append(f"💥 DESTROYED Obstacle {obstacle.id}!")
                    else:
                        hits.append(f"💥 HIT Obstacle {obstacle.id} (Health: {obstacle.health})")
                    break
    
    # Update game state
    game_state.birds_remaining -= 1
    game_state.score += points_scored
    
    # Record shot in history
    shot_info = {
        "angle": angle,
        "power": power,
        "hits": len(hits),
        "points": points_scored,
        "trajectory_length": len(trajectory)
    }
    game_history.append(shot_info)
    
    # Check win condition
    active_targets = [t for t in game_state.targets if not t.hit]
    if len(active_targets) == 0:
        game_state.game_over = True
        return f"""🎉 LEVEL COMPLETE!

🚀 Shot: {angle}° angle, {power}% power
{chr(10).join(hits) if hits else "💨 No hits this shot"}

🏆 ALL TARGETS DESTROYED!
Final Score: {game_state.score} points
Shots Used: {len(game_history)}/{5}
Efficiency: {game_state.score / len(game_history):.1f} points per shot

🎮 Use 'start_new_game' to play again!"""
    
    # Check game over condition
    if game_state.birds_remaining <= 0:
        game_state.game_over = True
        remaining_targets = len(active_targets)
        return f"""💀 GAME OVER!

🚀 Final Shot: {angle}° angle, {power}% power
{chr(10).join(hits) if hits else "💨 No hits this shot"}

📊 Final Results:
• Score: {game_state.score} points
• Targets Remaining: {remaining_targets}
• Shots Used: {len(game_history)}

🎮 Use 'start_new_game' to try again!"""
    
    # Regular shot result
    result = f"""🚀 Bird Launched!

📐 Shot Parameters:
• Angle: {angle}° (0° = horizontal, 90° = vertical)
• Power: {power}% of maximum
• Trajectory Points: {len(trajectory)}

🎯 Results:
{chr(10).join(hits) if hits else "💨 No hits this shot"}

📊 Current Status:
• Points This Shot: +{points_scored}
• Total Score: {game_state.score}
• Birds Remaining: {game_state.birds_remaining}
• Targets Left: {len(active_targets)}"""
    
    return result

@mcp.tool()
async def get_trajectory_preview(angle: float, power: float) -> str:
    """
    Preview the trajectory of a shot without launching.
    
    Args:
        angle: Launch angle in degrees (0-90)
        power: Launch power percentage (0-100)
        
    Returns:
        Predicted trajectory information
    """
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    # Validate inputs
    if not (0 <= angle <= 90):
        return "❌ Angle must be between 0 and 90 degrees!"
    
    if not (0 <= power <= 100):
        return "❌ Power must be between 0 and 100 percent!"
    
    # Calculate trajectory
    angle_rad = math.radians(angle)
    max_velocity = 15
    velocity_magnitude = (power / 100) * max_velocity
    
    velocity = Vector2(
        velocity_magnitude * math.cos(angle_rad),
        -velocity_magnitude * math.sin(angle_rad)
    )
    
    bird = Bird(
        position=Vector2(game_state.launcher_position.x, game_state.launcher_position.y),
        velocity=velocity
    )
    
    trajectory = simulate_physics(bird)
    
    if not trajectory:
        return "❌ Invalid trajectory calculated!"
    
    # Calculate trajectory stats
    max_height = min(point.y for point in trajectory)
    max_distance = max(point.x for point in trajectory)
    landing_point = trajectory[-1]
    
    # Check what might be hit
    potential_hits = []
    
    for target in game_state.targets:
        if not target.hit:
            for point in trajectory:
                dist = math.sqrt((point.x - target.position.x)**2 + (point.y - target.position.y)**2)
                if dist < (bird.radius + target.radius + 10):  # Add some tolerance for preview
                    potential_hits.append(f"🎯 Might hit Target {target.id} ({target.points} pts)")
                    break
    
    for obstacle in game_state.obstacles:
        if not obstacle.destroyed:
            for point in trajectory:
                if (obstacle.position.x - 10 <= point.x <= obstacle.position.x + obstacle.width + 10 and
                    obstacle.position.y - 10 <= point.y <= obstacle.position.y + obstacle.height + 10):
                    potential_hits.append(f"💥 Might hit Obstacle {obstacle.id}")
                    break
    
    return f"""🔮 Trajectory Preview:

📐 Shot Parameters:
• Angle: {angle}° 
• Power: {power}%
• Initial Velocity: {velocity_magnitude:.1f} pixels/frame

📊 Trajectory Stats:
• Max Height: {WORLD_HEIGHT - max_height:.0f} pixels above ground
• Max Distance: {max_distance:.0f} pixels
• Landing Point: ({landing_point.x:.0f}, {landing_point.y:.0f})
• Flight Time: ~{len(trajectory) * 0.1:.1f} seconds

🎯 Potential Targets:
{chr(10).join(potential_hits) if potential_hits else "💨 No targets in trajectory path"}

💡 Tip: Adjust angle for height, power for distance!"""

@mcp.tool()
async def analyze_targets() -> str:
    """
    Analyze all targets and provide strategic information.
    
    Returns:
        Detailed target analysis for strategic planning
    """
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    active_targets = [t for t in game_state.targets if not t.hit]
    
    if not active_targets:
        return "🎉 No targets remaining! All targets destroyed!"
    
    # Calculate distances and angles from launcher
    target_analysis = []
    
    for target in active_targets:
        # Distance from launcher
        dx = target.position.x - game_state.launcher_position.x
        dy = target.position.y - game_state.launcher_position.y
        distance = math.sqrt(dx**2 + dy**2)
        
        # Optimal angle (simplified physics)
        optimal_angle = math.degrees(math.atan2(-dy, dx))
        if optimal_angle < 0:
            optimal_angle = 0
        elif optimal_angle > 90:
            optimal_angle = 90
        
        # Points per distance ratio (efficiency)
        efficiency = target.points / distance if distance > 0 else 0
        
        # Check for obstacles in path
        obstacles_blocking = 0
        for obstacle in game_state.obstacles:
            if not obstacle.destroyed:
                # Simple line-of-sight check
                if (min(game_state.launcher_position.x, target.position.x) <= obstacle.position.x + obstacle.width and
                    max(game_state.launcher_position.x, target.position.x) >= obstacle.position.x):
                    obstacles_blocking += 1
        
        target_analysis.append({
            'target': target,
            'distance': distance,
            'optimal_angle': optimal_angle,
            'efficiency': efficiency,
            'obstacles_blocking': obstacles_blocking
        })
    
    # Sort by efficiency (points per distance)
    target_analysis.sort(key=lambda x: x['efficiency'], reverse=True)
    
    analysis = f"""🎯 Target Analysis ({len(active_targets)} remaining):

📊 Strategic Overview:
• Total Remaining Points: {sum(t.points for t in active_targets)}
• Average Distance: {sum(ta['distance'] for ta in target_analysis) / len(target_analysis):.0f} pixels

🎯 Targets (sorted by efficiency):"""
    
    for i, ta in enumerate(target_analysis, 1):
        target = ta['target']
        blocking_info = f" (⚠️ {ta['obstacles_blocking']} obstacles blocking)" if ta['obstacles_blocking'] > 0 else ""
        
        analysis += f"""
{i}. Target {target.id} - {target.type.upper()}
   • Points: {target.points} | Distance: {ta['distance']:.0f}px
   • Efficiency: {ta['efficiency']:.2f} pts/px
   • Suggested Angle: {ta['optimal_angle']:.0f}°{blocking_info}"""
    
    # Strategic recommendations
    best_target = target_analysis[0]
    analysis += f"""

💡 Strategic Recommendations:
🥇 Best Target: #{best_target['target'].id} ({best_target['efficiency']:.2f} efficiency)
🎯 Suggested First Shot: {best_target['optimal_angle']:.0f}° angle, 70-80% power
⚠️ Watch out for obstacles in your path!

🧠 Strategy Tips:
• High-value targets first if accessible
• Clear obstacles to open paths
• Save difficult shots for when you have fewer birds"""
    
    return analysis

@mcp.tool()
async def calculate_optimal_shot(target_id: int) -> str:
    """
    Calculate the optimal shot parameters for a specific target.
    
    Args:
        target_id: ID of the target to aim for
        
    Returns:
        Optimal angle and power calculations
    """
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    # Find the target
    target = None
    for t in game_state.targets:
        if t.id == target_id and not t.hit:
            target = t
            break
    
    if not target:
        return f"❌ Target {target_id} not found or already destroyed!"
    
    # Calculate physics for optimal shot
    dx = target.position.x - game_state.launcher_position.x
    dy = target.position.y - game_state.launcher_position.y
    
    # Use projectile motion equations
    g = GRAVITY
    
    # Try different angles and find the one requiring least power
    best_angle = 45
    best_power = 100
    
    for angle_deg in range(15, 76, 5):  # Test angles from 15° to 75°
        angle_rad = math.radians(angle_deg)
        
        # Calculate required initial velocity
        # Using: x = v₀cos(θ)t, y = v₀sin(θ)t - ½gt²
        # Solve for v₀ given target position
        
        cos_angle = math.cos(angle_rad)
        sin_angle = math.sin(angle_rad)
        
        if cos_angle == 0:
            continue
        
        # Time to reach target x position
        # We need to solve: dy = dx*tan(θ) - g*dx²/(2*v₀²*cos²(θ))
        # Rearranging: v₀² = g*dx²/(2*cos²(θ)*(dx*tan(θ) - dy))
        
        denominator = 2 * cos_angle**2 * (dx * math.tan(angle_rad) - dy)
        if denominator <= 0:
            continue
        
        v0_squared = g * dx**2 / denominator
        if v0_squared <= 0:
            continue
        
        v0 = math.sqrt(v0_squared)
        power_needed = (v0 / 15) * 100  # Convert to percentage
        
        if 0 < power_needed <= 100 and power_needed < best_power:
            best_angle = angle_deg
            best_power = power_needed
    
    # Validate the shot with trajectory simulation
    angle_rad = math.radians(best_angle)
    velocity_magnitude = (best_power / 100) * 15
    
    velocity = Vector2(
        velocity_magnitude * math.cos(angle_rad),
        -velocity_magnitude * math.sin(angle_rad)
    )
    
    bird = Bird(
        position=Vector2(game_state.launcher_position.x, game_state.launcher_position.y),
        velocity=velocity
    )
    
    trajectory = simulate_physics(bird)
    
    # Check if trajectory actually hits the target
    will_hit = False
    closest_distance = float('inf')
    
    for point in trajectory:
        dist = math.sqrt((point.x - target.position.x)**2 + (point.y - target.position.y)**2)
        closest_distance = min(closest_distance, dist)
        if dist < (bird.radius + target.radius):
            will_hit = True
            break
    
    hit_probability = "HIGH" if will_hit else "MEDIUM" if closest_distance < 50 else "LOW"
    
    return f"""🎯 Optimal Shot Calculation for Target {target_id}:

📐 Target Information:
• Position: ({target.position.x:.0f}, {target.position.y:.0f})
• Type: {target.type} | Points: {target.points}
• Distance: {math.sqrt(dx**2 + dy**2):.0f} pixels

🚀 Recommended Shot:
• Angle: {best_angle}°
• Power: {best_power:.0f}%
• Hit Probability: {hit_probability}
• Closest Approach: {closest_distance:.0f} pixels

📊 Physics Calculation:
• Horizontal Distance: {dx:.0f} pixels
• Vertical Distance: {dy:.0f} pixels
• Required Initial Velocity: {velocity_magnitude:.1f} pixels/frame

💡 Alternative Shots:
• Lower Angle: {max(15, best_angle - 10)}° at {min(100, best_power + 15):.0f}% power
• Higher Angle: {min(75, best_angle + 10)}° at {min(100, best_power + 10):.0f}% power

⚠️ Note: Calculations assume no obstacles. Check trajectory preview first!"""

@mcp.tool()
async def get_game_help() -> str:
    """
    Get help information about the physics game.
    
    Returns:
        Comprehensive game help and strategy guide
    """
    return """🎮 Physics Game Help & Strategy Guide

🎯 Game Objective:
Destroy all targets using limited birds with physics-based projectile motion!

🚀 Available Commands:
• start_new_game() - Begin a new game session
• get_game_state() - Check current status
• launch_bird(angle, power) - Fire a shot
• get_trajectory_preview(angle, power) - Preview shot path
• analyze_targets() - Strategic target analysis
• calculate_optimal_shot(target_id) - Physics-based shot calculation

📐 Physics Mechanics:
• Gravity: {GRAVITY} pixels/frame² (affects trajectory)
• Launch Angles: 0° (horizontal) to 90° (vertical)
• Power: 0% (gentle) to 100% (maximum force)
• Collision: Birds hit targets and obstacles

🎯 Scoring System:
• Normal Targets: 100 points
• Bonus Targets: 150-200 points
• Efficiency Bonus: Higher score with fewer shots
• Obstacle Destruction: Clears paths to targets

💡 Strategy Tips:
1. **Analyze First**: Use analyze_targets() to plan
2. **Preview Shots**: Check trajectory before launching
3. **High-Value Targets**: Prioritize bonus targets
4. **Clear Obstacles**: Remove blocking obstacles first
5. **Angle Selection**:
   - Low angles (15-30°): Fast, direct shots
   - Medium angles (30-60°): Balanced trajectory
   - High angles (60-75°): Arc over obstacles

🧮 Physics Formulas:
• Trajectory: x = v₀cos(θ)t, y = v₀sin(θ)t - ½gt²
• Range: R = v₀²sin(2θ)/g (in vacuum)
• Max Height: H = v₀²sin²(θ)/(2g)

🎮 Game Flow:
1. Start game → Analyze targets → Plan strategy
2. Calculate optimal shots → Preview trajectory
3. Launch birds → Adapt strategy → Repeat
4. Achieve high score with efficient shooting!

🏆 Mastery Goals:
• Complete levels with minimal shots
• Achieve high efficiency ratings
• Master physics calculations
• Develop strategic thinking

Ready to become a physics game master? Start with 'start_new_game()'! 🚀"""

def main():
    """Run the Physics Game MCP server"""
    print("🎮 Starting Physics Game MCP Server...")
    print("Available tools: start_new_game, get_game_state, launch_bird, get_trajectory_preview,")
    print("                analyze_targets, calculate_optimal_shot, get_game_help")
    print("Server is ready for epic physics gaming!")
    print("Use Ctrl+C to stop the server.")
    
    # Run the server with stdio transport
    mcp.run(transport="stdio")

if __name__ == "__main__":
    main()
