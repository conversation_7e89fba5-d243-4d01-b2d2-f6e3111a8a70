<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Grid Bot Adventure</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: 'Exo 2', sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e0e6ed;
            min-height: 100vh;
            overflow-x: auto;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 4px;
            background: linear-gradient(90deg, transparent, #00d4ff, #ff0080, transparent);
            border-radius: 2px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px #00d4ff; }
            to { box-shadow: 0 0 20px #ff0080, 0 0 30px #00d4ff; }
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-size: 3em;
            font-weight: 900;
            margin: 20px 0 10px 0;
            background: linear-gradient(45deg, #00d4ff, #ff0080, #00ff88);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: rainbow 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        @keyframes rainbow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header p {
            font-size: 1.3em;
            margin: 10px 0;
            opacity: 0.9;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        
        .game-container {
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }

        .game-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .game-canvas {
            border: 3px solid transparent;
            border-radius: 15px;
            box-shadow:
                0 0 30px rgba(0, 212, 255, 0.3),
                0 0 60px rgba(255, 0, 128, 0.2),
                inset 0 0 30px rgba(0, 255, 136, 0.1);
            background: linear-gradient(145deg, #1e2a3a, #2c3e50);
            position: relative;
            overflow: hidden;
        }

        .game-canvas::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00d4ff, #ff0080, #00ff88, #00d4ff);
            border-radius: 15px;
            z-index: -1;
            animation: borderGlow 3s linear infinite;
        }

        @keyframes borderGlow {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .game-info {
            flex: 1;
            background: linear-gradient(145deg,
                rgba(30, 42, 58, 0.9),
                rgba(44, 62, 80, 0.8)
            );
            padding: 25px;
            border-radius: 15px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.1);
        }
        
        .info-section {
            margin-bottom: 25px;
            position: relative;
        }

        .info-section h3 {
            margin: 0 0 15px 0;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.4em;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 12px 15px;
            background: linear-gradient(90deg,
                rgba(0, 212, 255, 0.1),
                rgba(255, 0, 128, 0.05)
            );
            border-radius: 8px;
            border-left: 3px solid #00d4ff;
            transition: all 0.3s ease;
        }

        .status-item:hover {
            background: linear-gradient(90deg,
                rgba(0, 212, 255, 0.2),
                rgba(255, 0, 128, 0.1)
            );
            transform: translateX(5px);
        }

        .status-value {
            font-weight: 600;
            color: #00ff88;
            text-shadow: 0 0 5px rgba(0, 255, 136, 0.5);
        }
        
        .controls {
            background: linear-gradient(145deg,
                rgba(0, 212, 255, 0.1),
                rgba(255, 0, 128, 0.05)
            );
            padding: 20px;
            border-radius: 12px;
            margin-top: 15px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .btn {
            background: linear-gradient(45deg, #ff0080, #ff4060, #ff6040);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 30px;
            cursor: pointer;
            font-family: 'Exo 2', sans-serif;
            font-size: 1em;
            font-weight: 600;
            margin: 6px;
            transition: all 0.3s ease;
            box-shadow:
                0 4px 15px rgba(255, 0, 128, 0.3),
                inset 0 1px 0 rgba(255,255,255,0.2);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow:
                0 8px 25px rgba(255, 0, 128, 0.4),
                0 0 20px rgba(255, 0, 128, 0.3);
            background: linear-gradient(45deg, #ff0080, #ff2060, #ff4040);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .direction-btn {
            background: linear-gradient(45deg, #00d4ff, #0080ff, #4060ff);
            width: 70px;
            height: 70px;
            border-radius: 50%;
            font-size: 1.8em;
            margin: 4px;
            box-shadow:
                0 4px 15px rgba(0, 212, 255, 0.3),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .direction-btn:hover {
            background: linear-gradient(45deg, #00d4ff, #0060ff, #2040ff);
            box-shadow:
                0 8px 25px rgba(0, 212, 255, 0.4),
                0 0 20px rgba(0, 212, 255, 0.3);
        }

        .laser-btn {
            background: linear-gradient(45deg, #ff4040, #ff6020, #ff8000);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 4px 15px rgba(255, 64, 64, 0.3); }
            50% { box-shadow: 0 8px 25px rgba(255, 64, 64, 0.6), 0 0 30px rgba(255, 64, 64, 0.4); }
        }
        
        .gamepad-container {
            background: linear-gradient(145deg,
                rgba(0, 212, 255, 0.15),
                rgba(255, 0, 128, 0.1)
            );
            padding: 30px;
            border-radius: 20px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            box-shadow:
                0 10px 30px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
        }

        .gamepad-title {
            text-align: center;
            font-family: 'Orbitron', monospace;
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gamepad-layout {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 40px;
            max-width: 600px;
        }

        .direction-pad {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr 1fr 1fr;
            gap: 8px;
            width: 220px;
            height: 220px;
        }

        .direction-pad .btn:nth-child(2) { grid-column: 2; grid-row: 1; } /* North */
        .direction-pad .btn:nth-child(1) { grid-column: 1; grid-row: 2; } /* West */
        .direction-pad .btn:nth-child(3) { grid-column: 3; grid-row: 2; } /* East */
        .direction-pad .btn:nth-child(4) { grid-column: 2; grid-row: 3; } /* South */

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .laser-pad {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr 1fr 1fr;
            gap: 6px;
            width: 180px;
            height: 180px;
        }

        .laser-pad .btn:nth-child(2) { grid-column: 2; grid-row: 1; } /* North */
        .laser-pad .btn:nth-child(1) { grid-column: 1; grid-row: 2; } /* West */
        .laser-pad .btn:nth-child(3) { grid-column: 3; grid-row: 2; } /* East */
        .laser-pad .btn:nth-child(4) { grid-column: 2; grid-row: 3; } /* South */

        .gamepad-label {
            font-family: 'Exo 2', sans-serif;
            font-weight: 600;
            font-size: 1.1em;
            text-align: center;
            margin-bottom: 10px;
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        
        .mcp-info {
            background: rgba(0,255,0,0.1);
            border: 1px solid rgba(0,255,0,0.3);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .legend {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-emoji {
            font-size: 1.5em;
            margin-right: 10px;
            width: 30px;
        }
        
        .game-log {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Grid Bot Adventure</h1>
            <p>Strategic grid-based game controlled by AI through MCP</p>
            <p><strong>LLM navigates the bot to collect coins while avoiding turtle enemies!</strong></p>
        </div>
        
        <div class="game-container">
            <div class="game-area">
                <div id="game-canvas-container">
                    <!-- P5.js canvas will be inserted here -->
                </div>

                <!-- Gamepad Controls -->
                <div class="gamepad-container">
                    <div class="gamepad-title">🎮 GAME CONTROLS</div>
                    <div class="gamepad-layout">
                        <!-- Movement D-Pad -->
                        <div>
                            <div class="gamepad-label">🚶 MOVEMENT</div>
                            <div class="direction-pad">
                                <button class="btn direction-btn" onclick="moveBot('west')">⬅️</button>
                                <button class="btn direction-btn" onclick="moveBot('north')">⬆️</button>
                                <button class="btn direction-btn" onclick="moveBot('east')">➡️</button>
                                <button class="btn direction-btn" onclick="moveBot('south')">⬇️</button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn" onclick="newGame()">🎮 NEW GAME</button>
                            <button class="btn" onclick="lookAround()">🔍 LOOK</button>
                            <button class="btn" onclick="analyzeThreats()">🧠 ANALYZE</button>
                        </div>

                        <!-- Laser D-Pad -->
                        <div>
                            <div class="gamepad-label">🔥 LASER WEAPON</div>
                            <div class="laser-pad">
                                <button class="btn laser-btn" onclick="fireLaser('west')">⬅️🔥</button>
                                <button class="btn laser-btn" onclick="fireLaser('north')">⬆️🔥</button>
                                <button class="btn laser-btn" onclick="fireLaser('east')">➡️🔥</button>
                                <button class="btn laser-btn" onclick="fireLaser('south')">⬇️🔥</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="game-info">
                <div class="info-section">
                    <h3>🎯 Game Status</h3>
                    <div class="status-item">
                        <span>💰 Score:</span>
                        <span class="status-value" id="score">0</span>
                    </div>
                    <div class="status-item">
                        <span>🪙 Coins Remaining:</span>
                        <span class="status-value" id="coins">8</span>
                    </div>
                    <div class="status-item">
                        <span>👣 Moves Made:</span>
                        <span class="status-value" id="moves">0</span>
                    </div>
                    <div class="status-item">
                        <span>🔄 Turn:</span>
                        <span class="status-value" id="turn">0</span>
                    </div>
                    <div class="status-item">
                        <span>🤖 Bot Health:</span>
                        <span class="status-value" id="bot-health">3/3 ❤️</span>
                    </div>
                    <div class="status-item">
                        <span>⚡ Laser Status:</span>
                        <span class="status-value" id="laser-status">Ready 🔥</span>
                    </div>
                </div>
                
                <div class="legend">
                    <h3>🗺️ Game Legend</h3>
                    <div class="legend-item">
                        <span class="legend-emoji">🤖</span>
                        <span>Your Bot</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🐢</span>
                        <span>Green Turtle (Random)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🔴</span>
                        <span>Red Turtle (Chaser)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🔵</span>
                        <span>Blue Turtle (Patrol)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🪙</span>
                        <span>Coin (10 pts)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🏆</span>
                        <span>Golden Coin (50 pts)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">⬛</span>
                        <span>Wall</span>
                    </div>
                </div>
                
                <div class="controls">
                    <h3>🎮 Quick Actions</h3>
                    <button class="btn" onclick="newGame()">🎮 New Game</button>
                    <button class="btn" onclick="lookAround()">🔍 Look Around</button>
                    <button class="btn" onclick="analyzeThreats()">🧠 Analyze Threats</button>

                    <div style="margin-top: 15px; padding: 15px; background: rgba(0,255,136,0.1); border-radius: 8px; border: 1px solid rgba(0,255,136,0.3);">
                        <h4 style="margin: 0 0 10px 0; color: #00ff88;">💡 Pro Tip</h4>
                        <p style="margin: 0; font-size: 0.9em; opacity: 0.9;">Use the gamepad controls below the grid for movement and laser attacks!</p>
                    </div>
                </div>
                
                <div class="game-log" id="game-log">
                    <div>🎮 Game ready! Start a new game to begin.</div>
                </div>
                
                <div class="mcp-info">
                    <h3>🤖 MCP Integration</h3>
                    <p><strong>This game is controlled by AI!</strong></p>
                    <p>The LLM uses these MCP tools:</p>
                    <ul>
                        <li><code>start_new_game()</code></li>
                        <li><code>move_bot(direction)</code></li>
                        <li><code>look_around()</code></li>
                        <li><code>analyze_threats()</code></li>
                        <li><code>find_nearest_coin()</code></li>
                    </ul>
                    <p>Try asking Claude: <em>"Start a new grid game and analyze the best strategy to collect coins safely!"</em></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            grid: [],
            bot: { x: 2, y: 2, alive: true, hits: 0, maxHits: 3, laserCooldown: 0 },
            score: 0,
            coins: 8,
            moves: 0,
            turn: 0,
            gameOver: false,
            victory: false,
            laserBeam: null // For laser animation
        };

        const GRID_SIZE = 10;
        const CELL_SIZE = 50;

        function setup() {
            let canvas = createCanvas(GRID_SIZE * CELL_SIZE, GRID_SIZE * CELL_SIZE);
            canvas.parent('game-canvas-container');
            canvas.class('game-canvas');
            
            initializeGame();
        }

        function draw() {
            // Animated background
            background(15, 15, 35);

            // Add subtle animated background pattern
            for (let i = 0; i < 20; i++) {
                let x = (frameCount * 0.5 + i * 50) % (width + 100);
                let y = (frameCount * 0.3 + i * 30) % (height + 100);
                fill(0, 212, 255, 10);
                noStroke();
                circle(x, y, 3);
            }

            // Draw grid
            drawGrid();

            // Draw game elements
            drawGameElements();

            // Draw laser beam if active
            if (gameState.laserBeam) {
                drawLaserBeam();
            }

            // Draw grid lines with glow effect
            stroke(0, 212, 255, 80);
            strokeWeight(1);
            for (let i = 0; i <= GRID_SIZE; i++) {
                line(i * CELL_SIZE, 0, i * CELL_SIZE, height);
                line(0, i * CELL_SIZE, width, i * CELL_SIZE);
            }

            // Reduce laser cooldown
            if (gameState.bot.laserCooldown > 0) {
                gameState.bot.laserCooldown--;
            }
        }

        function drawGrid() {
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    let cellX = x * CELL_SIZE;
                    let cellY = y * CELL_SIZE;
                    
                    // Draw cell background
                    if (gameState.grid[y] && gameState.grid[y][x]) {
                        let cell = gameState.grid[y][x];
                        
                        if (cell === '⬛') {
                            fill(50, 50, 50);
                            rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                        } else {
                            fill(70, 130, 180, 50);
                            rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                        }
                    } else {
                        fill(70, 130, 180, 50);
                        rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                    }
                }
            }
        }

        function drawGameElements() {
            textAlign(CENTER, CENTER);
            textSize(32);

            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    if (gameState.grid[y] && gameState.grid[y][x]) {
                        let cell = gameState.grid[y][x];
                        let cellX = x * CELL_SIZE + CELL_SIZE / 2;
                        let cellY = y * CELL_SIZE + CELL_SIZE / 2;

                        if (cell !== '⬜' && cell !== '⬛') {
                            // Add glow effect for special elements
                            if (cell === '🤖') {
                                // Bot glow effect
                                fill(0, 255, 136, 50);
                                noStroke();
                                circle(cellX, cellY, 45);

                                // Health indicator
                                let healthWidth = 30;
                                let healthHeight = 4;
                                let healthX = cellX - healthWidth/2;
                                let healthY = cellY - 25;

                                // Health bar background
                                fill(255, 0, 0, 100);
                                rect(healthX, healthY, healthWidth, healthHeight);

                                // Health bar fill
                                let healthPercent = (gameState.bot.maxHits - gameState.bot.hits) / gameState.bot.maxHits;
                                fill(0, 255, 0, 200);
                                rect(healthX, healthY, healthWidth * healthPercent, healthHeight);
                            } else if (cell === '🪙' || cell === '🏆') {
                                // Coin glow effect
                                fill(255, 215, 0, 30 + sin(frameCount * 0.1) * 20);
                                noStroke();
                                circle(cellX, cellY, 40);
                            } else if (cell.includes('🐢') || cell.includes('🔴') || cell.includes('🔵')) {
                                // Enemy glow effect
                                fill(255, 0, 128, 20 + sin(frameCount * 0.15) * 15);
                                noStroke();
                                circle(cellX, cellY, 42);
                            }

                            // Draw the emoji
                            fill(255);
                            text(cell, cellX, cellY);
                        }
                    }
                }
            }
        }

        function drawLaserBeam() {
            if (!gameState.laserBeam) return;

            let beam = gameState.laserBeam;
            let startX = beam.startX * CELL_SIZE + CELL_SIZE / 2;
            let startY = beam.startY * CELL_SIZE + CELL_SIZE / 2;
            let endX = beam.endX * CELL_SIZE + CELL_SIZE / 2;
            let endY = beam.endY * CELL_SIZE + CELL_SIZE / 2;

            // Laser beam effect
            strokeWeight(8);
            stroke(255, 0, 0, 200);
            line(startX, startY, endX, endY);

            strokeWeight(4);
            stroke(255, 100, 100, 255);
            line(startX, startY, endX, endY);

            strokeWeight(2);
            stroke(255, 255, 255, 255);
            line(startX, startY, endX, endY);

            // Reduce beam duration
            beam.duration--;
            if (beam.duration <= 0) {
                gameState.laserBeam = null;
            }
        }

        function initializeGame() {
            // Initialize empty grid
            gameState.grid = [];
            for (let y = 0; y < GRID_SIZE; y++) {
                gameState.grid[y] = [];
                for (let x = 0; x < GRID_SIZE; x++) {
                    if (x === 0 || x === GRID_SIZE-1 || y === 0 || y === GRID_SIZE-1) {
                        gameState.grid[y][x] = '⬛';
                    } else {
                        gameState.grid[y][x] = '⬜';
                    }
                }
            }
            
            // Add some random walls
            for (let i = 0; i < 6; i++) {
                let x = Math.floor(Math.random() * (GRID_SIZE - 4)) + 2;
                let y = Math.floor(Math.random() * (GRID_SIZE - 4)) + 2;
                gameState.grid[y][x] = '⬛';
            }
            
            // Place bot
            gameState.bot = { x: 2, y: 2, alive: true };
            gameState.grid[2][2] = '🤖';
            
            // Place turtles
            gameState.grid[7][7] = '🐢';
            gameState.grid[3][8] = '🔴';
            gameState.grid[8][3] = '🔵';
            
            // Place coins - ensure they're placed correctly
            let coinPositions = [
                [4, 4], [6, 2], [2, 6], [5, 7], [7, 4], [3, 3], [6, 6], [1, 8]
            ];

            let coinsPlaced = 0;
            coinPositions.forEach(([x, y], index) => {
                if (x > 0 && x < GRID_SIZE-1 && y > 0 && y < GRID_SIZE-1 &&
                    gameState.grid[y][x] === '⬜') {
                    // First 2 coins are golden, rest are regular
                    gameState.grid[y][x] = index < 2 ? '🏆' : '🪙';
                    coinsPlaced++;
                }
            });

            // Add more coins if needed
            while (coinsPlaced < 8) {
                let x = Math.floor(Math.random() * (GRID_SIZE - 4)) + 2;
                let y = Math.floor(Math.random() * (GRID_SIZE - 4)) + 2;
                if (gameState.grid[y][x] === '⬜') {
                    gameState.grid[y][x] = coinsPlaced < 2 ? '🏆' : '🪙';
                    coinsPlaced++;
                }
            }
            
            // Reset game state
            gameState.score = 0;
            gameState.coins = 8;
            gameState.moves = 0;
            gameState.turn = 0;
            gameState.gameOver = false;
            gameState.victory = false;
            gameState.bot.hits = 0;
            gameState.bot.laserCooldown = 0;
            gameState.laserBeam = null;

            updateUI();
        }

        function updateUI() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('coins').textContent = gameState.coins;
            document.getElementById('moves').textContent = gameState.moves;
            document.getElementById('turn').textContent = gameState.turn;

            // Update health display - fix the NaN issue
            let currentHealth = gameState.bot.maxHits - gameState.bot.hits;
            let healthText = `${currentHealth}/${gameState.bot.maxHits}`;

            if (gameState.bot.hits === 0) healthText += ' ❤️❤️❤️';
            else if (gameState.bot.hits === 1) healthText += ' ❤️❤️🖤';
            else if (gameState.bot.hits === 2) healthText += ' ❤️🖤🖤';
            else healthText += ' 💀💀💀';

            document.getElementById('bot-health').textContent = healthText;

            // Update laser status
            let laserText = gameState.bot.laserCooldown > 0 ?
                `Cooldown ${gameState.bot.laserCooldown} ⏳` : 'Ready 🔥';
            document.getElementById('laser-status').textContent = laserText;

            // Check for game over
            if (gameState.bot.hits >= gameState.bot.maxHits && !gameState.gameOver) {
                gameState.gameOver = true;
                gameState.bot.alive = false;
                addToLog('💀 GAME OVER! Bot destroyed after taking 3 hits!');
                addToLog('🎮 Click "New Game" to try again!');
            }
        }

        function addToLog(message) {
            const log = document.getElementById('game-log');
            const div = document.createElement('div');
            div.textContent = message;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }

        function moveBot(direction) {
            if (gameState.gameOver) {
                addToLog('❌ Game is over! Start a new game to play again.');
                return;
            }

            let newX = gameState.bot.x;
            let newY = gameState.bot.y;

            switch(direction) {
                case 'north': newY--; break;
                case 'south': newY++; break;
                case 'east': newX++; break;
                case 'west': newX--; break;
            }

            // Check bounds and walls
            if (newX < 0 || newX >= GRID_SIZE || newY < 0 || newY >= GRID_SIZE ||
                gameState.grid[newY][newX] === '⬛') {
                addToLog(`❌ Cannot move ${direction} - blocked by wall or boundary!`);
                return;
            }

            // Check what's at the target position
            let cellContent = gameState.grid[newY][newX];

            // Clear old position
            gameState.grid[gameState.bot.y][gameState.bot.x] = '⬜';

            // Check for enemy collision
            if (cellContent === '🐢' || cellContent === '🔴' || cellContent === '🔵') {
                gameState.bot.hits++;
                let remainingHealth = gameState.bot.maxHits - gameState.bot.hits;
                addToLog(`💥 Hit by enemy ${cellContent}! Health: ${remainingHealth}/${gameState.bot.maxHits}`);

                if (gameState.bot.hits >= gameState.bot.maxHits) {
                    gameState.gameOver = true;
                    gameState.bot.alive = false;
                    // Place bot at new position even when dying
                    gameState.bot.x = newX;
                    gameState.bot.y = newY;
                    gameState.grid[newY][newX] = '💀'; // Show dead bot
                    addToLog('💀 GAME OVER! Bot destroyed after taking 3 hits!');
                    addToLog('🎮 Click "NEW GAME" to try again!');
                    updateUI();
                    return;
                }
            }

            // Check for coin collection
            if (cellContent === '🪙') {
                gameState.score += 10;
                gameState.coins--;
                addToLog('🪙 Collected regular coin! +10 points');
            } else if (cellContent === '🏆') {
                gameState.score += 50;
                gameState.coins--;
                addToLog('🏆 Collected golden coin! +50 points');
            }

            // Move bot
            gameState.bot.x = newX;
            gameState.bot.y = newY;
            gameState.grid[newY][newX] = '🤖';
            gameState.moves++;
            gameState.turn++;

            // Reduce laser cooldown
            if (gameState.bot.laserCooldown > 0) {
                gameState.bot.laserCooldown--;
            }

            // Check victory
            if (gameState.coins === 0 && !gameState.gameOver) {
                gameState.gameOver = true;
                gameState.victory = true;
                addToLog('🎉 VICTORY! All coins collected successfully!');
                addToLog(`🏆 Final Score: ${gameState.score} points in ${gameState.moves} moves!`);
            }

            updateUI();
            addToLog(`🤖 Bot moved ${direction} to (${newX}, ${newY})`);
        }

        function fireLaser(direction) {
            if (gameState.gameOver) {
                addToLog('❌ Game is over! Start a new game to play again.');
                return;
            }

            if (gameState.bot.laserCooldown > 0) {
                addToLog(`⏳ Laser cooling down: ${gameState.bot.laserCooldown} turns remaining`);
                return;
            }

            let startX = gameState.bot.x;
            let startY = gameState.bot.y;
            let dx = 0, dy = 0;

            switch(direction) {
                case 'north': dy = -1; break;
                case 'south': dy = 1; break;
                case 'east': dx = 1; break;
                case 'west': dx = -1; break;
            }

            // Find first target in laser path
            let currentX = startX + dx;
            let currentY = startY + dy;
            let hit = false;
            let endX = startX;
            let endY = startY;

            while (currentX >= 0 && currentX < GRID_SIZE &&
                   currentY >= 0 && currentY < GRID_SIZE) {

                endX = currentX;
                endY = currentY;

                let cell = gameState.grid[currentY][currentX];

                // Hit wall - laser stops
                if (cell === '⬛') {
                    break;
                }

                // Hit enemy - destroy it
                if (cell === '🐢' || cell === '🔴' || cell === '🔵') {
                    gameState.grid[currentY][currentX] = '⬜';
                    addToLog(`🔥 Laser destroyed enemy ${cell}!`);
                    hit = true;
                    break;
                }

                // Hit coin - destroy it (oops!)
                if (cell === '🪙' || cell === '🏆') {
                    gameState.grid[currentY][currentX] = '⬜';
                    gameState.coins--;
                    addToLog(`💥 Laser accidentally destroyed coin ${cell}!`);
                    hit = true;
                    break;
                }

                currentX += dx;
                currentY += dy;
            }

            // Create laser beam animation
            gameState.laserBeam = {
                startX: startX,
                startY: startY,
                endX: endX,
                endY: endY,
                duration: 30 // frames
            };

            // Set cooldown
            gameState.bot.laserCooldown = 3;

            if (!hit) {
                addToLog(`🔥 Laser fired ${direction} - no targets hit`);
            }

            updateUI();
        }

        function newGame() {
            initializeGame();
            addToLog('🎮 New game started!');
        }

        function lookAround() {
            let botX = gameState.bot.x;
            let botY = gameState.bot.y;
            
            let surroundings = [];
            let directions = [
                ['North', 0, -1], ['South', 0, 1], 
                ['East', 1, 0], ['West', -1, 0]
            ];
            
            directions.forEach(([dir, dx, dy]) => {
                let x = botX + dx;
                let y = botY + dy;
                if (x >= 0 && x < GRID_SIZE && y >= 0 && y < GRID_SIZE) {
                    let cell = gameState.grid[y][x];
                    surroundings.push(`${dir}: ${cell}`);
                }
            });
            
            addToLog(`🔍 Looking around: ${surroundings.join(', ')}`);
        }

        function analyzeThreats() {
            // Simple threat analysis
            let threats = [];
            let coins = [];
            
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    let cell = gameState.grid[y][x];
                    let distance = Math.abs(x - gameState.bot.x) + Math.abs(y - gameState.bot.y);
                    
                    if (cell === '🐢' || cell === '🔴' || cell === '🔵') {
                        threats.push(`${cell} at distance ${distance}`);
                    } else if (cell === '🪙' || cell === '🏆') {
                        coins.push(`${cell} at distance ${distance}`);
                    }
                }
            }
            
            addToLog(`🧠 Analysis - Threats: ${threats.join(', ')} | Coins: ${coins.join(', ')}`);
        }

        // Keyboard controls
        function keyPressed() {
            switch(keyCode) {
                case UP_ARROW: moveBot('north'); break;
                case DOWN_ARROW: moveBot('south'); break;
                case LEFT_ARROW: moveBot('west'); break;
                case RIGHT_ARROW: moveBot('east'); break;
            }
        }
    </script>
</body>
</html>
