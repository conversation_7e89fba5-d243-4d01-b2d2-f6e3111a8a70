<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Grid Bot Adventure</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header p {
            font-size: 1.2em;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .game-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .game-canvas {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            background: #2c3e50;
        }
        
        .game-info {
            flex: 1;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-section h3 {
            margin: 0 0 10px 0;
            color: #FFD700;
            font-size: 1.3em;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .controls {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .btn {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .direction-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 1.5em;
            margin: 2px;
        }
        
        .direction-controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr 1fr 1fr;
            gap: 5px;
            width: 200px;
            margin: 10px auto;
        }
        
        .direction-controls .btn:nth-child(2) { grid-column: 2; grid-row: 1; } /* North */
        .direction-controls .btn:nth-child(1) { grid-column: 1; grid-row: 2; } /* West */
        .direction-controls .btn:nth-child(3) { grid-column: 3; grid-row: 2; } /* East */
        .direction-controls .btn:nth-child(4) { grid-column: 2; grid-row: 3; } /* South */
        
        .mcp-info {
            background: rgba(0,255,0,0.1);
            border: 1px solid rgba(0,255,0,0.3);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .legend {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-emoji {
            font-size: 1.5em;
            margin-right: 10px;
            width: 30px;
        }
        
        .game-log {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Grid Bot Adventure</h1>
            <p>Strategic grid-based game controlled by AI through MCP</p>
            <p><strong>LLM navigates the bot to collect coins while avoiding turtle enemies!</strong></p>
        </div>
        
        <div class="game-container">
            <div id="game-canvas-container">
                <!-- P5.js canvas will be inserted here -->
            </div>
            
            <div class="game-info">
                <div class="info-section">
                    <h3>🎯 Game Status</h3>
                    <div class="status-item">
                        <span>Score:</span>
                        <span id="score">0</span>
                    </div>
                    <div class="status-item">
                        <span>Coins Remaining:</span>
                        <span id="coins">8</span>
                    </div>
                    <div class="status-item">
                        <span>Moves Made:</span>
                        <span id="moves">0</span>
                    </div>
                    <div class="status-item">
                        <span>Turn:</span>
                        <span id="turn">0</span>
                    </div>
                    <div class="status-item">
                        <span>Bot Status:</span>
                        <span id="bot-status">✅ Alive</span>
                    </div>
                </div>
                
                <div class="legend">
                    <h3>🗺️ Game Legend</h3>
                    <div class="legend-item">
                        <span class="legend-emoji">🤖</span>
                        <span>Your Bot</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🐢</span>
                        <span>Green Turtle (Random)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🔴</span>
                        <span>Red Turtle (Chaser)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🔵</span>
                        <span>Blue Turtle (Patrol)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🪙</span>
                        <span>Coin (10 pts)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">🏆</span>
                        <span>Golden Coin (50 pts)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji">⬛</span>
                        <span>Wall</span>
                    </div>
                </div>
                
                <div class="controls">
                    <h3>🎮 Manual Controls</h3>
                    <div class="direction-controls">
                        <button class="btn direction-btn" onclick="moveBot('west')">⬅️</button>
                        <button class="btn direction-btn" onclick="moveBot('north')">⬆️</button>
                        <button class="btn direction-btn" onclick="moveBot('east')">➡️</button>
                        <button class="btn direction-btn" onclick="moveBot('south')">⬇️</button>
                    </div>
                    <button class="btn" onclick="newGame()">🎮 New Game</button>
                    <button class="btn" onclick="lookAround()">🔍 Look Around</button>
                    <button class="btn" onclick="analyzeThreats()">🧠 Analyze</button>
                </div>
                
                <div class="game-log" id="game-log">
                    <div>🎮 Game ready! Start a new game to begin.</div>
                </div>
                
                <div class="mcp-info">
                    <h3>🤖 MCP Integration</h3>
                    <p><strong>This game is controlled by AI!</strong></p>
                    <p>The LLM uses these MCP tools:</p>
                    <ul>
                        <li><code>start_new_game()</code></li>
                        <li><code>move_bot(direction)</code></li>
                        <li><code>look_around()</code></li>
                        <li><code>analyze_threats()</code></li>
                        <li><code>find_nearest_coin()</code></li>
                    </ul>
                    <p>Try asking Claude: <em>"Start a new grid game and analyze the best strategy to collect coins safely!"</em></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            grid: [],
            bot: { x: 5, y: 5, alive: true },
            score: 0,
            coins: 8,
            moves: 0,
            turn: 0,
            gameOver: false
        };

        const GRID_SIZE = 10;
        const CELL_SIZE = 50;

        function setup() {
            let canvas = createCanvas(GRID_SIZE * CELL_SIZE, GRID_SIZE * CELL_SIZE);
            canvas.parent('game-canvas-container');
            canvas.class('game-canvas');
            
            initializeGame();
        }

        function draw() {
            background(44, 62, 80);
            
            // Draw grid
            drawGrid();
            
            // Draw game elements
            drawGameElements();
            
            // Draw grid lines
            stroke(255, 255, 255, 100);
            strokeWeight(1);
            for (let i = 0; i <= GRID_SIZE; i++) {
                line(i * CELL_SIZE, 0, i * CELL_SIZE, height);
                line(0, i * CELL_SIZE, width, i * CELL_SIZE);
            }
        }

        function drawGrid() {
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    let cellX = x * CELL_SIZE;
                    let cellY = y * CELL_SIZE;
                    
                    // Draw cell background
                    if (gameState.grid[y] && gameState.grid[y][x]) {
                        let cell = gameState.grid[y][x];
                        
                        if (cell === '⬛') {
                            fill(50, 50, 50);
                            rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                        } else {
                            fill(70, 130, 180, 50);
                            rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                        }
                    } else {
                        fill(70, 130, 180, 50);
                        rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                    }
                }
            }
        }

        function drawGameElements() {
            textAlign(CENTER, CENTER);
            textSize(32);
            
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    if (gameState.grid[y] && gameState.grid[y][x]) {
                        let cell = gameState.grid[y][x];
                        let cellX = x * CELL_SIZE + CELL_SIZE / 2;
                        let cellY = y * CELL_SIZE + CELL_SIZE / 2;
                        
                        if (cell !== '⬜' && cell !== '⬛') {
                            text(cell, cellX, cellY);
                        }
                    }
                }
            }
        }

        function initializeGame() {
            // Initialize empty grid
            gameState.grid = [];
            for (let y = 0; y < GRID_SIZE; y++) {
                gameState.grid[y] = [];
                for (let x = 0; x < GRID_SIZE; x++) {
                    if (x === 0 || x === GRID_SIZE-1 || y === 0 || y === GRID_SIZE-1) {
                        gameState.grid[y][x] = '⬛';
                    } else {
                        gameState.grid[y][x] = '⬜';
                    }
                }
            }
            
            // Add some random walls
            for (let i = 0; i < 6; i++) {
                let x = Math.floor(Math.random() * (GRID_SIZE - 4)) + 2;
                let y = Math.floor(Math.random() * (GRID_SIZE - 4)) + 2;
                gameState.grid[y][x] = '⬛';
            }
            
            // Place bot
            gameState.bot = { x: 2, y: 2, alive: true };
            gameState.grid[2][2] = '🤖';
            
            // Place turtles
            gameState.grid[7][7] = '🐢';
            gameState.grid[3][8] = '🔴';
            gameState.grid[8][3] = '🔵';
            
            // Place coins
            let coinPositions = [
                [4, 4], [6, 2], [2, 6], [8, 8], [5, 7], [7, 4], [3, 3], [6, 6]
            ];
            
            coinPositions.forEach(([x, y], index) => {
                if (gameState.grid[y][x] === '⬜') {
                    gameState.grid[y][x] = index < 2 ? '🏆' : '🪙';
                }
            });
            
            // Reset game state
            gameState.score = 0;
            gameState.coins = 8;
            gameState.moves = 0;
            gameState.turn = 0;
            gameState.gameOver = false;
            
            updateUI();
        }

        function updateUI() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('coins').textContent = gameState.coins;
            document.getElementById('moves').textContent = gameState.moves;
            document.getElementById('turn').textContent = gameState.turn;
            document.getElementById('bot-status').textContent = gameState.bot.alive ? '✅ Alive' : '💀 Dead';
        }

        function addToLog(message) {
            const log = document.getElementById('game-log');
            const div = document.createElement('div');
            div.textContent = message;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }

        function moveBot(direction) {
            if (gameState.gameOver || !gameState.bot.alive) {
                addToLog('❌ Game is over or bot is dead!');
                return;
            }
            
            let newX = gameState.bot.x;
            let newY = gameState.bot.y;
            
            switch(direction) {
                case 'north': newY--; break;
                case 'south': newY++; break;
                case 'east': newX++; break;
                case 'west': newX--; break;
            }
            
            // Check bounds and walls
            if (newX < 0 || newX >= GRID_SIZE || newY < 0 || newY >= GRID_SIZE ||
                gameState.grid[newY][newX] === '⬛') {
                addToLog(`❌ Cannot move ${direction} - blocked!`);
                return;
            }
            
            // Clear old position
            gameState.grid[gameState.bot.y][gameState.bot.x] = '⬜';
            
            // Check for coin collection
            let cellContent = gameState.grid[newY][newX];
            if (cellContent === '🪙') {
                gameState.score += 10;
                gameState.coins--;
                addToLog('🪙 Collected coin! +10 points');
            } else if (cellContent === '🏆') {
                gameState.score += 50;
                gameState.coins--;
                addToLog('🏆 Collected golden coin! +50 points');
            }
            
            // Move bot
            gameState.bot.x = newX;
            gameState.bot.y = newY;
            gameState.grid[newY][newX] = '🤖';
            gameState.moves++;
            gameState.turn++;
            
            // Check for turtle collision (simplified)
            // In real game, turtles would move too
            
            // Check victory
            if (gameState.coins === 0) {
                gameState.gameOver = true;
                addToLog('🎉 VICTORY! All coins collected!');
            }
            
            updateUI();
            addToLog(`🤖 Bot moved ${direction} to (${newX}, ${newY})`);
        }

        function newGame() {
            initializeGame();
            addToLog('🎮 New game started!');
        }

        function lookAround() {
            let botX = gameState.bot.x;
            let botY = gameState.bot.y;
            
            let surroundings = [];
            let directions = [
                ['North', 0, -1], ['South', 0, 1], 
                ['East', 1, 0], ['West', -1, 0]
            ];
            
            directions.forEach(([dir, dx, dy]) => {
                let x = botX + dx;
                let y = botY + dy;
                if (x >= 0 && x < GRID_SIZE && y >= 0 && y < GRID_SIZE) {
                    let cell = gameState.grid[y][x];
                    surroundings.push(`${dir}: ${cell}`);
                }
            });
            
            addToLog(`🔍 Looking around: ${surroundings.join(', ')}`);
        }

        function analyzeThreats() {
            // Simple threat analysis
            let threats = [];
            let coins = [];
            
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    let cell = gameState.grid[y][x];
                    let distance = Math.abs(x - gameState.bot.x) + Math.abs(y - gameState.bot.y);
                    
                    if (cell === '🐢' || cell === '🔴' || cell === '🔵') {
                        threats.push(`${cell} at distance ${distance}`);
                    } else if (cell === '🪙' || cell === '🏆') {
                        coins.push(`${cell} at distance ${distance}`);
                    }
                }
            }
            
            addToLog(`🧠 Analysis - Threats: ${threats.join(', ')} | Coins: ${coins.join(', ')}`);
        }

        // Keyboard controls
        function keyPressed() {
            switch(keyCode) {
                case UP_ARROW: moveBot('north'); break;
                case DOWN_ARROW: moveBot('south'); break;
                case LEFT_ARROW: moveBot('west'); break;
                case RIGHT_ARROW: moveBot('east'); break;
            }
        }
    </script>
</body>
</html>
