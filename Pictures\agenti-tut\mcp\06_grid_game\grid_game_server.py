#!/usr/bin/env python3
"""
🤖 Grid Bot Adventure MCP Server

An MCP server for a strategic grid-based game where an LLM controls a bot
to collect coins while avoiding turtle enemies!

Features:
- Turn-based strategic gameplay
- Multiple enemy types with different AI
- Coin collection objectives
- Safe pathfinding challenges
- Real-time game state updates
"""

import math
import random
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass, field
from mcp.server import FastMCP

# Create MCP server instance
mcp = FastMCP("Grid Bot Adventure Server")

# Game constants
GRID_WIDTH = 10
GRID_HEIGHT = 10
EMPTY = "⬜"
WALL = "⬛"
BOT = "🤖"
TURTLE_GREEN = "🐢"
TURTLE_RED = "🔴"
TURTLE_BLUE = "🔵"
COIN = "🪙"
GOLDEN_COIN = "🏆"
SAFE_ZONE = "🟢"

@dataclass
class Position:
    x: int
    y: int
    
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y
    
    def distance_to(self, other):
        return abs(self.x - other.x) + abs(self.y - other.y)

@dataclass
class Bot:
    position: Position
    alive: bool = True
    moves_made: int = 0

@dataclass
class Turtle:
    position: Position
    turtle_type: str  # "green", "red", "blue"
    direction: str = "north"  # For patrol patterns
    chase_range: int = 3
    
    def get_emoji(self):
        return {"green": TURTLE_GREEN, "red": TURTLE_RED, "blue": TURTLE_BLUE}[self.turtle_type]

@dataclass
class Coin:
    position: Position
    value: int = 10
    is_golden: bool = False
    
    def get_emoji(self):
        return GOLDEN_COIN if self.is_golden else COIN

@dataclass
class GameState:
    grid: List[List[str]] = field(default_factory=list)
    bot: Optional[Bot] = None
    turtles: List[Turtle] = field(default_factory=list)
    coins: List[Coin] = field(default_factory=list)
    score: int = 0
    level: int = 1
    game_over: bool = False
    victory: bool = False
    turn_count: int = 0

# Global game state
game_state = None

def create_empty_grid():
    """Create an empty grid with walls around the border"""
    grid = []
    for y in range(GRID_HEIGHT):
        row = []
        for x in range(GRID_WIDTH):
            if x == 0 or x == GRID_WIDTH-1 or y == 0 or y == GRID_HEIGHT-1:
                row.append(WALL)
            else:
                row.append(EMPTY)
        grid.append(row)
    return grid

def add_random_walls(grid, count=8):
    """Add some random walls for obstacles"""
    for _ in range(count):
        while True:
            x = random.randint(2, GRID_WIDTH-3)
            y = random.randint(2, GRID_HEIGHT-3)
            if grid[y][x] == EMPTY:
                grid[y][x] = WALL
                break

def is_valid_position(x, y):
    """Check if position is within grid bounds and not a wall"""
    if not (0 <= x < GRID_WIDTH and 0 <= y < GRID_HEIGHT):
        return False
    return game_state.grid[y][x] not in [WALL]

def get_adjacent_positions(pos):
    """Get valid adjacent positions"""
    directions = [(0, -1), (0, 1), (-1, 0), (1, 0)]  # north, south, west, east
    adjacent = []
    for dx, dy in directions:
        new_x, new_y = pos.x + dx, pos.y + dy
        if is_valid_position(new_x, new_y):
            adjacent.append(Position(new_x, new_y))
    return adjacent

def find_safe_positions():
    """Find positions that are safe from all turtles"""
    safe_positions = []
    for y in range(1, GRID_HEIGHT-1):
        for x in range(1, GRID_WIDTH-1):
            if is_valid_position(x, y):
                pos = Position(x, y)
                is_safe = True
                for turtle in game_state.turtles:
                    if pos.distance_to(turtle.position) <= turtle.chase_range:
                        is_safe = False
                        break
                if is_safe:
                    safe_positions.append(pos)
    return safe_positions

def move_turtle(turtle):
    """Move a turtle based on its type and behavior"""
    if turtle.turtle_type == "green":
        # Random movement
        adjacent = get_adjacent_positions(turtle.position)
        if adjacent:
            turtle.position = random.choice(adjacent)
    
    elif turtle.turtle_type == "red":
        # Chase player if within range
        bot_pos = game_state.bot.position
        distance = turtle.position.distance_to(bot_pos)
        
        if distance <= turtle.chase_range:
            # Move toward player
            dx = bot_pos.x - turtle.position.x
            dy = bot_pos.y - turtle.position.y
            
            # Choose best direction
            if abs(dx) > abs(dy):
                new_x = turtle.position.x + (1 if dx > 0 else -1)
                new_y = turtle.position.y
            else:
                new_x = turtle.position.x
                new_y = turtle.position.y + (1 if dy > 0 else -1)
            
            if is_valid_position(new_x, new_y):
                turtle.position = Position(new_x, new_y)
        else:
            # Random movement when not chasing
            adjacent = get_adjacent_positions(turtle.position)
            if adjacent:
                turtle.position = random.choice(adjacent)
    
    elif turtle.turtle_type == "blue":
        # Patrol pattern
        directions = {"north": (0, -1), "south": (0, 1), "west": (-1, 0), "east": (1, 0)}
        dx, dy = directions[turtle.direction]
        new_x = turtle.position.x + dx
        new_y = turtle.position.y + dy
        
        if is_valid_position(new_x, new_y):
            turtle.position = Position(new_x, new_y)
        else:
            # Change direction when hitting obstacle
            turtle.direction = random.choice(["north", "south", "west", "east"])

def update_grid_display():
    """Update the visual grid with current positions"""
    # Reset grid to empty/walls
    for y in range(GRID_HEIGHT):
        for x in range(GRID_WIDTH):
            if game_state.grid[y][x] not in [WALL]:
                game_state.grid[y][x] = EMPTY
    
    # Place coins
    for coin in game_state.coins:
        game_state.grid[coin.position.y][coin.position.x] = coin.get_emoji()
    
    # Place turtles
    for turtle in game_state.turtles:
        game_state.grid[turtle.position.y][turtle.position.x] = turtle.get_emoji()
    
    # Place bot (on top of everything)
    if game_state.bot and game_state.bot.alive:
        game_state.grid[game_state.bot.position.y][game_state.bot.position.x] = BOT

def initialize_game():
    """Initialize a new game"""
    global game_state
    
    # Create grid
    grid = create_empty_grid()
    add_random_walls(grid, 6)
    
    # Create bot at random safe position
    while True:
        x = random.randint(2, GRID_WIDTH-3)
        y = random.randint(2, GRID_HEIGHT-3)
        if grid[y][x] == EMPTY:
            bot = Bot(Position(x, y))
            break
    
    # Create turtles
    turtles = []
    turtle_types = ["green", "red", "blue"]
    for i in range(3):
        while True:
            x = random.randint(2, GRID_WIDTH-3)
            y = random.randint(2, GRID_HEIGHT-3)
            pos = Position(x, y)
            if grid[y][x] == EMPTY and pos.distance_to(bot.position) > 3:
                turtle_type = turtle_types[i % len(turtle_types)]
                turtles.append(Turtle(pos, turtle_type))
                break
    
    # Create coins
    coins = []
    for _ in range(8):
        while True:
            x = random.randint(1, GRID_WIDTH-2)
            y = random.randint(1, GRID_HEIGHT-2)
            pos = Position(x, y)
            if (grid[y][x] == EMPTY and 
                pos.distance_to(bot.position) > 2 and
                all(pos.distance_to(t.position) > 1 for t in turtles)):
                is_golden = random.random() < 0.2  # 20% chance for golden coin
                value = 50 if is_golden else 10
                coins.append(Coin(pos, value, is_golden))
                break
    
    game_state = GameState(
        grid=grid,
        bot=bot,
        turtles=turtles,
        coins=coins,
        score=0,
        level=1,
        game_over=False,
        victory=False,
        turn_count=0
    )
    
    update_grid_display()

def format_grid():
    """Format the grid for display"""
    result = "🗺️ Game Grid:\n"
    result += "  " + "".join(f"{i}" for i in range(GRID_WIDTH)) + "\n"
    
    for y, row in enumerate(game_state.grid):
        result += f"{y} " + "".join(row) + "\n"
    
    return result

@mcp.tool()
def start_new_game() -> str:
    """Start a new grid bot adventure game."""
    initialize_game()
    
    return f"""🤖 Grid Bot Adventure Started!

🎯 Mission: Collect all coins while avoiding turtle enemies!

📊 Game Setup:
• Grid Size: {GRID_WIDTH}x{GRID_HEIGHT}
• Bot Position: ({game_state.bot.position.x}, {game_state.bot.position.y})
• Turtles: {len(game_state.turtles)} enemies
• Coins: {len(game_state.coins)} to collect
• Score: {game_state.score}

🐢 Enemy Types:
• {TURTLE_GREEN} Green: Moves randomly
• {TURTLE_RED} Red: Chases when nearby
• {TURTLE_BLUE} Blue: Patrols in patterns

{format_grid()}

💡 Use 'move_bot(direction)' to move (north/south/east/west)
🔍 Use 'look_around()' to analyze your surroundings
Ready for adventure! 🚀"""

@mcp.tool()
def get_game_state() -> str:
    """Get the current game state and grid."""
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    status = f"""📊 Current Game State:

🤖 Bot Status:
• Position: ({game_state.bot.position.x}, {game_state.bot.position.y})
• Alive: {'✅' if game_state.bot.alive else '💀'}
• Moves Made: {game_state.bot.moves_made}

🎯 Objectives:
• Score: {game_state.score} points
• Coins Remaining: {len(game_state.coins)}
• Turn: {game_state.turn_count}

🐢 Enemies:
"""
    
    for i, turtle in enumerate(game_state.turtles):
        status += f"• Turtle {i+1} {turtle.get_emoji()}: ({turtle.position.x}, {turtle.position.y}) - {turtle.turtle_type}\n"
    
    if game_state.coins:
        status += f"\n🪙 Coins:\n"
        for i, coin in enumerate(game_state.coins):
            status += f"• Coin {i+1} {coin.get_emoji()}: ({coin.position.x}, {coin.position.y}) - {coin.value} pts\n"
    
    status += f"\n{format_grid()}"
    
    if game_state.game_over:
        if game_state.victory:
            status += "\n🎉 VICTORY! All coins collected!"
        else:
            status += "\n💀 GAME OVER! Bot was caught by a turtle!"
    
    return status

@mcp.tool()
def move_bot(direction: str) -> str:
    """Move the bot in the specified direction."""
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    if game_state.game_over:
        return "❌ Game is over! Use 'start_new_game' to play again."
    
    if not game_state.bot.alive:
        return "❌ Bot is not alive! Game over."
    
    # Parse direction
    direction = direction.lower().strip()
    direction_map = {
        "north": (0, -1), "up": (0, -1),
        "south": (0, 1), "down": (0, 1),
        "east": (1, 0), "right": (1, 0),
        "west": (-1, 0), "left": (-1, 0)
    }
    
    if direction not in direction_map:
        return f"❌ Invalid direction '{direction}'. Use: north, south, east, west"
    
    dx, dy = direction_map[direction]
    new_x = game_state.bot.position.x + dx
    new_y = game_state.bot.position.y + dy
    
    # Check if move is valid
    if not is_valid_position(new_x, new_y):
        return f"❌ Cannot move {direction} - blocked by wall or out of bounds!"
    
    # Move bot
    old_pos = game_state.bot.position
    game_state.bot.position = Position(new_x, new_y)
    game_state.bot.moves_made += 1
    game_state.turn_count += 1
    
    # Check for coin collection
    coins_collected = []
    for coin in game_state.coins[:]:  # Copy list to avoid modification during iteration
        if coin.position == game_state.bot.position:
            coins_collected.append(coin)
            game_state.coins.remove(coin)
            game_state.score += coin.value
    
    # Move turtles
    for turtle in game_state.turtles:
        move_turtle(turtle)
    
    # Check for collisions with turtles
    for turtle in game_state.turtles:
        if turtle.position == game_state.bot.position:
            game_state.bot.alive = False
            game_state.game_over = True
            update_grid_display()
            return f"""💀 GAME OVER!

🤖 Bot moved {direction} from ({old_pos.x}, {old_pos.y}) to ({new_x}, {new_y})
🐢 Caught by {turtle.get_emoji()} {turtle.turtle_type} turtle!

📊 Final Stats:
• Score: {game_state.score} points
• Moves: {game_state.bot.moves_made}
• Coins Collected: {8 - len(game_state.coins)}/8

🎮 Use 'start_new_game' to try again!"""
    
    # Check victory condition
    if len(game_state.coins) == 0:
        game_state.victory = True
        game_state.game_over = True
        update_grid_display()
        return f"""🎉 VICTORY!

🤖 Bot moved {direction} and collected the final coin!
🏆 All coins collected successfully!

📊 Final Stats:
• Score: {game_state.score} points
• Moves: {game_state.bot.moves_made}
• Efficiency: {game_state.score / game_state.bot.moves_made:.1f} points per move

🎮 Congratulations! Use 'start_new_game' for another challenge!"""
    
    # Update grid display
    update_grid_display()
    
    # Format result
    result = f"""🤖 Bot moved {direction}!

📍 Position: ({old_pos.x}, {old_pos.y}) → ({new_x}, {new_y})
"""
    
    if coins_collected:
        for coin in coins_collected:
            result += f"🪙 Collected {coin.get_emoji()} worth {coin.value} points!\n"
        result += f"💰 Score: {game_state.score} points\n"
    
    result += f"🎯 Coins remaining: {len(game_state.coins)}\n"
    result += f"🐢 Turtles moved to new positions\n"
    
    return result

@mcp.tool()
def look_around() -> str:
    """Look around and analyze the current surroundings."""
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    bot_pos = game_state.bot.position
    
    # Find nearby objects
    nearby_turtles = []
    nearby_coins = []
    
    for turtle in game_state.turtles:
        distance = bot_pos.distance_to(turtle.position)
        if distance <= 4:  # Within 4 grid spaces
            nearby_turtles.append((turtle, distance))
    
    for coin in game_state.coins:
        distance = bot_pos.distance_to(coin.position)
        if distance <= 5:  # Within 5 grid spaces
            nearby_coins.append((coin, distance))
    
    # Sort by distance
    nearby_turtles.sort(key=lambda x: x[1])
    nearby_coins.sort(key=lambda x: x[1])
    
    result = f"""🔍 Looking around from position ({bot_pos.x}, {bot_pos.y}):

🐢 Nearby Threats:"""
    
    if nearby_turtles:
        for turtle, distance in nearby_turtles:
            threat_level = "🚨 DANGER" if distance <= 2 else "⚠️ CAUTION" if distance <= 3 else "👀 WATCH"
            result += f"\n• {turtle.get_emoji()} {turtle.turtle_type.title()} turtle at ({turtle.position.x}, {turtle.position.y}) - Distance: {distance} - {threat_level}"
    else:
        result += "\n• No immediate threats detected ✅"
    
    result += f"\n\n🪙 Nearby Coins:"
    
    if nearby_coins:
        for coin, distance in nearby_coins:
            result += f"\n• {coin.get_emoji()} at ({coin.position.x}, {coin.position.y}) - Distance: {distance} - Value: {coin.value} pts"
    else:
        result += "\n• No coins in immediate vicinity"
    
    # Check adjacent cells
    result += f"\n\n🧭 Adjacent Cells:"
    directions = [("North", 0, -1), ("South", 0, 1), ("East", 1, 0), ("West", -1, 0)]
    
    for direction, dx, dy in directions:
        new_x, new_y = bot_pos.x + dx, bot_pos.y + dy
        if is_valid_position(new_x, new_y):
            cell_content = game_state.grid[new_y][new_x]
            if cell_content == EMPTY:
                result += f"\n• {direction}: Clear ✅"
            else:
                result += f"\n• {direction}: {cell_content}"
        else:
            result += f"\n• {direction}: Blocked ❌"
    
    return result

@mcp.tool()
def analyze_threats() -> str:
    """Analyze all threats and provide strategic recommendations."""
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    bot_pos = game_state.bot.position
    
    analysis = f"""🧠 Threat Analysis:

🤖 Bot Position: ({bot_pos.x}, {bot_pos.y})

🐢 Enemy Analysis:"""
    
    for i, turtle in enumerate(game_state.turtles):
        distance = bot_pos.distance_to(turtle.position)
        
        if turtle.turtle_type == "green":
            behavior = "Random movement - unpredictable"
            threat = "Low" if distance > 3 else "Medium"
        elif turtle.turtle_type == "red":
            behavior = f"Chases when within {turtle.chase_range} spaces"
            threat = "HIGH" if distance <= turtle.chase_range else "Low"
        else:  # blue
            behavior = "Patrols in patterns - predictable"
            threat = "Medium" if distance <= 2 else "Low"
        
        analysis += f"""
• Turtle {i+1} {turtle.get_emoji()}:
  - Position: ({turtle.position.x}, {turtle.position.y})
  - Distance: {distance} spaces
  - Type: {turtle.turtle_type.title()}
  - Behavior: {behavior}
  - Threat Level: {threat}"""
    
    # Strategic recommendations
    analysis += f"\n\n💡 Strategic Recommendations:"
    
    # Find safest directions
    safe_directions = []
    directions = [("north", 0, -1), ("south", 0, 1), ("east", 1, 0), ("west", -1, 0)]
    
    for direction, dx, dy in directions:
        new_x, new_y = bot_pos.x + dx, bot_pos.y + dy
        if is_valid_position(new_x, new_y):
            new_pos = Position(new_x, new_y)
            min_turtle_distance = min(new_pos.distance_to(t.position) for t in game_state.turtles)
            safe_directions.append((direction, min_turtle_distance))
    
    safe_directions.sort(key=lambda x: x[1], reverse=True)
    
    if safe_directions:
        safest_dir, safest_dist = safe_directions[0]
        analysis += f"\n• Safest direction: {safest_dir} (min turtle distance: {safest_dist})"
        
        if safest_dist <= 2:
            analysis += "\n• ⚠️ All directions have nearby threats - proceed with caution!"
        elif safest_dist >= 4:
            analysis += "\n• ✅ Safe movement options available"
    
    # Find nearest coin
    if game_state.coins:
        nearest_coin = min(game_state.coins, key=lambda c: bot_pos.distance_to(c.position))
        coin_distance = bot_pos.distance_to(nearest_coin.position)
        analysis += f"\n• Nearest coin: {nearest_coin.get_emoji()} at ({nearest_coin.position.x}, {nearest_coin.position.y}) - {coin_distance} spaces away"
        
        # Check if path to coin is safe
        path_safe = True
        for turtle in game_state.turtles:
            if turtle.turtle_type == "red" and turtle.position.distance_to(nearest_coin.position) <= turtle.chase_range:
                path_safe = False
                break
        
        if path_safe:
            analysis += "\n• ✅ Path to nearest coin appears safe"
        else:
            analysis += "\n• ⚠️ Path to nearest coin may be dangerous"
    
    return analysis

@mcp.tool()
def find_nearest_coin() -> str:
    """Find the nearest coin and suggest a path."""
    if not game_state:
        return "❌ No game in progress. Use 'start_new_game' to begin!"
    
    if not game_state.coins:
        return "🎉 No coins remaining! All collected!"
    
    bot_pos = game_state.bot.position
    
    # Find nearest coin
    nearest_coin = min(game_state.coins, key=lambda c: bot_pos.distance_to(c.position))
    distance = bot_pos.distance_to(nearest_coin.position)
    
    # Calculate simple path (Manhattan distance)
    dx = nearest_coin.position.x - bot_pos.x
    dy = nearest_coin.position.y - bot_pos.y
    
    path_suggestions = []
    if dx > 0:
        path_suggestions.append(f"Move east {dx} times")
    elif dx < 0:
        path_suggestions.append(f"Move west {abs(dx)} times")
    
    if dy > 0:
        path_suggestions.append(f"Move south {dy} times")
    elif dy < 0:
        path_suggestions.append(f"Move north {abs(dy)} times")
    
    result = f"""🪙 Nearest Coin Analysis:

🎯 Target: {nearest_coin.get_emoji()} {'Golden Coin' if nearest_coin.is_golden else 'Regular Coin'}
📍 Position: ({nearest_coin.position.x}, {nearest_coin.position.y})
💰 Value: {nearest_coin.value} points
📏 Distance: {distance} moves (Manhattan distance)

🗺️ Suggested Path:
{' → '.join(path_suggestions) if path_suggestions else 'Already at coin position!'}

⚠️ Threat Assessment:"""
    
    # Check for threats near the coin
    threats_near_coin = []
    for turtle in game_state.turtles:
        turtle_distance = turtle.position.distance_to(nearest_coin.position)
        if turtle_distance <= 3:
            threats_near_coin.append((turtle, turtle_distance))
    
    if threats_near_coin:
        result += "\n🚨 Threats detected near target coin:"
        for turtle, dist in threats_near_coin:
            result += f"\n• {turtle.get_emoji()} {turtle.turtle_type} turtle {dist} spaces away"
        result += "\n💡 Consider alternative route or wait for turtles to move"
    else:
        result += "\n✅ No immediate threats near target coin"
    
    return result

@mcp.tool()
def get_game_help() -> str:
    """Get help information about the grid bot game."""
    return """🤖 Grid Bot Adventure Help

🎯 Objective:
Collect all coins while avoiding turtle enemies!

🎮 Controls:
• move_bot(direction) - Move north/south/east/west
• look_around() - Examine surroundings
• analyze_threats() - Strategic threat analysis
• find_nearest_coin() - Locate closest coin

🐢 Enemy Types:
• 🐢 Green Turtle: Moves randomly, unpredictable
• 🔴 Red Turtle: Chases player when nearby (3 space range)
• 🔵 Blue Turtle: Patrols in patterns, predictable

🪙 Coins:
• 🪙 Regular Coin: 10 points
• 🏆 Golden Coin: 50 points (rare)

💡 Strategy Tips:
1. **Scout First**: Use look_around() before moving
2. **Avoid Red Turtles**: They chase when you're close
3. **Plan Routes**: Use analyze_threats() for safe paths
4. **Golden Priority**: Golden coins are worth 5x more
5. **Corner Safety**: Use walls to limit turtle approach angles

🏆 Scoring:
• Collect all coins to win
• Fewer moves = higher efficiency
• Avoid getting caught by turtles

Ready to become a coin collecting master? 🚀"""

if __name__ == "__main__":
    print("🤖 Starting Grid Bot Adventure MCP Server...")
    print("Available tools: start_new_game, move_bot, get_game_state, look_around,")
    print("                analyze_threats, find_nearest_coin, get_game_help")
    print("Server is ready for strategic grid-based gaming!")
    print("Use Ctrl+C to stop the server.")
    
    # Run the server with stdio transport
    mcp.run(transport="stdio")
